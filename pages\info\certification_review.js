// pages/info/certification_review.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 关注微信公众号
   */
  followWechat() {
    wx.showModal({
      title: '关注公众号',
      content: '请在微信中搜索"找车侠"公众号并关注，输入"审核"获取最新认证动态',
      showCancel: true,
      cancelText: '取消',
      confirmText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 可以在这里添加复制公众号名称到剪贴板的功能
          wx.setClipboardData({
            data: '找车侠',
            success: () => {
              wx.showToast({
                title: '公众号名称已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 撤回审核
   */
  withdrawReview() {
    wx.showModal({
      title: '撤回审核',
      content: '确定要撤回当前的企业认证审核吗？撤回后需要重新提交认证资料。',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定撤回',
      confirmColor: '#FF6B35',
      success: (res) => {
        if (res.confirm) {
          // 显示加载提示
          wx.showLoading({
            title: '撤回中...',
            mask: true
          });

          // 模拟撤回请求
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '撤回成功',
              icon: 'success',
              duration: 2000
            });

            // 延迟返回到企业认证页面
            setTimeout(() => {
              wx.redirectTo({
                url: '/pages/info/certification'
              });
            }, 2000);
          }, 1500);
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 模拟刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '已是最新状态',
        icon: 'none'
      });
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '企业认证中',
      path: '/pages/info/certification_review'
    };
  }
})