// 导入 API 模块
import api from '../../utils/api';
import util from '../../utils/util';
import cosUpload from '../../utils/cos-upload';
import { areaList } from '@vant/area-data';
import share from '../../utils/share';  // 确保导入 分享

// 获取应用实例
const app = getApp()

Page({
    behaviors: [share], //分享设置
    data: {
        //处理分享页面 统一写
        shareData: {
            title: '我的页面',
            path: '/pages/my/index',
            isDetailPage: false, // 标记是否详情页
        },
        userInfo: null,
        hasUserInfo: false,
        isLoading: true,  // 添加加载状态
        showLoadingAnimation: false, // 控制显示普通加载还是骨架屏
        roleMap: {
            "vehicle": '车源商',
            "service": '服务商',
            "trade": '汽配商',
            "traders": '汽配商',
            "purchase": '采购商',
            "1": '经销商',
            "2": '个人用户',
            "3": '检测员'
        },
        // 添加推荐车辆数据
        recommendCars: [], // 改为空数组，等待接口数据填充
        // 添加认证状态映射
        authStatusMap: {
            '1': {
                text: '认证商户',
                class: 'auth-success'
            },
            '2': {
                text: '未认证',
                class: 'auth-warning'
            },
            '3': {
                text: '认证中',
                class: 'auth-pending'
            }
        },
        statusBarHeight: 20, // 默认状态栏高度
        showLoginPopup: false, // 控制登录弹窗显示
        loginPopupOptions: {
            title: '登录后即可使用此功能',
            buttonText: '立即登录',
            imageUrl: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274411926_900.png'
        },
        // 客服弹窗相关数据
        showCustomerServicePopup: false, // 控制客服弹窗显示
        customerServicePhone: '18613019729', // 客服电话
        // 添加官方客服弹窗数据
        showOfficialServicePopup: false, // 控制官方客服QR码弹窗
        qrCodeUrl: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/covers/20250724/56c0b5663730579689d35d8d88909e50.png' // 替换为实际的QR码图片链接
    },

    async onLoad() {
        // 获取状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        this.setData({
            statusBarHeight: systemInfo.statusBarHeight
        });

        // 立即检查登录状态
        this.checkLoginStatus();

        // 先显示骨架屏
        this.setData({
            isLoading: true,
            showLoadingAnimation: false
        });

        // 获取推荐车辆
        await this.getRecommendCars();

        // 设置短暂延时后关闭加载状态
        setTimeout(() => {
            this.setData({
                isLoading: false
            });
        }, 800); // 增加延时时间，确保骨架屏效果明显
    },

    // 获取推荐车辆
    async getRecommendCars() {
        try {
            const result = await api.car.getCarList({
                page: 1,
                list_rows: 10  // 获取10条数据
            });

            if (result && result.data) {
                const carListData = Array.isArray(result.data) ? result.data : [];

                // 随机打乱数组顺序
                const shuffledCars = this.shuffleArray(carListData);

                // 只取前3条数据
                const recommendCars = shuffledCars.slice(0, 3).map(car => ({
                    id: car.id,
                    badge: '新车',  // 或根据实际数据设置
                    title: car.ui_vehicle_name || car.title_desc,
                    year: car.first_registration_time ? car.first_registration_time.substring(0, 4) : '',
                    mileage: (car.mileage || 0).toString(),
                    price: car.sell_price || '0',
                    originalPrice: car.official_price || '0',
                    image: car.main_url || (car.image_urls ? car.image_urls.split(',')[0] : ''),
                    location: car.vehicle_source_location || '',
                    date: car.create_time || new Date().toISOString().substring(0, 10)
                }));

                this.setData({ recommendCars });
            }
        } catch (error) {
            console.error('获取推荐车辆失败:', error);
        }
    },

    // 数组随机打乱方法
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    },

    // 修改车辆点击事件处理方法
    onCarTap(e) {
        const { id } = e.currentTarget.dataset;

        // 检查登录状态
        const userInfo = util.getUserInfo();
        if (!userInfo || !userInfo.app_id) {
            // 未登录，直接跳转到登录页面，并传递车辆ID作为重定向参数
            wx.navigateTo({
                url: `/pages/login/index?redirect=/pages/buy/detail&id=${id}`
            });
            return;
        }

        // 已登录，直接跳转到详情页
        wx.navigateTo({
            url: `/pages/buy/detail?id=${id}`
        });
    },

    onShow() {
        // 页面显示时也更新登录状态，但不显示骨架屏
        this.checkLoginStatus();
    },

    // 检查登录状态
    checkLoginStatus() {
        const userInfo = util.getUserInfo();
        // console.log('用户信息:', userInfo);

        this.setData({
            userInfo: userInfo || {},
            hasUserInfo: !!(userInfo && userInfo.is_info === 1)
        });
    },

    // 登录点击事件
    onLoginTap() {
        wx.navigateTo({
            url: '/pages/login/index'
        });
    },

    // 退出登录
    logout() {
        wx.showModal({
            title: '提示',
            content: '确定要退出登录吗？',
            success: (res) => {
                if (res.confirm) {
                    // 清除用户信息
                    wx.removeStorageSync('userInfo');
                    // 清除其他可能的用户相关缓存
                    wx.removeStorageSync('token');
                    wx.removeStorageSync('sessionKey');
                    // 清除侠友圈发布草稿
                    wx.removeStorageSync('momentDraft');

                    this.setData({
                        userInfo: {},
                        hasUserInfo: false
                    });

                    // 显示退出成功提示
                    wx.showToast({
                        title: '已退出登录',
                        icon: 'success',
                        duration: 2000
                    });

                    // 重新加载页面
                    this.onLoad();
                }
            }
        });
    },

    // 设置点击事件
    onSettingTap() {
        wx.navigateTo({
            url: '/pages/my/settings/index'
        });
    },

    // 页面导航
    navigateTo(e) {
        const url = e.currentTarget.dataset.url;

        // 检查是否需要登录
        if (this.needLogin(url) && !this.data.hasUserInfo) {
            wx.showToast({
                title: '请先登录',
                icon: 'none',
                duration: 2000
            });

            setTimeout(() => {
                wx.navigateTo({
                    url: '/pages/login/index'
                });
            }, 1000);

            return;
        }

        wx.navigateTo({
            url: url
        });
    },

    // 判断页面是否需要登录
    needLogin(url) {
        // 这些页面需要登录
        const needLoginPages = [
            '/pages/my/orders/index',
            '/pages/my/favorites/index',
            '/pages/my/address/index',
            '/pages/my/evaluation/index'
        ];

        return needLoginPages.some(page => url.startsWith(page));
    },

    // 选择并上传头像
    chooseAvatar() {
        wx.chooseImage({
            count: 1, // 默认9
            sizeType: ['compressed'], // 指定压缩图
            sourceType: ['album', 'camera'], // 可以从相册选择或拍照
            success: (res) => {
                // 获取临时文件路径
                const tempFilePath = res.tempFilePaths[0];

                // 显示上传中提示
                wx.showLoading({
                    title: '上传中...',
                    mask: true
                });

                // 上传图片到腾讯云COS
                cosUpload.uploadFile(res.tempFilePaths[0], 'my')
                    .then(result => {
                        // 上传成功，保存图片URL
                        const userInfo = { ...this.data.userInfo };
                        userInfo.logo = result.url;

                        // 更新本地存储的用户信息
                        util.setCacheWithExpiry('userInfo', userInfo);  // 默认7天过期

                        //保存图片到服务器
                        // console.log(result)
                        api.user.updateUserLogo({
                            avatar: "/" + result.key,
                            app_id: userInfo.app_id
                        }).then(result => {
                            console.log(result)
                        })


                        // 更新页面数据
                        this.setData({ userInfo });

                        wx.hideLoading();
                        wx.showToast({
                            title: '上传成功',
                            icon: 'success'
                        });
                    })
                    .catch(error => {
                        console.error('上传失败:', error);
                        wx.hideLoading();
                        wx.showToast({
                            title: '上传失败',
                            icon: 'none'
                        });
                    });
            }
        });
    },

    // 企业认证
    goCertification() {
        if (!this.data.hasUserInfo) {
            wx.showToast({
                title: '请先登录',
                icon: 'none'
            });
            return;
        }

        wx.navigateTo({
            url: '/pages/my/certification/index'
        });
    },

    // 收藏车辆
    toggleFavorite(e) {
        const carId = e.currentTarget.dataset.id;
        // 实际应用中应该调用API
        // console.log('收藏/取消收藏车辆:', carId);

        // 这里只是演示，实际应调用API
        // api.toggleFavorite(carId).then(res => {
        //     // 更新收藏状态
        // });
    },

    // 查看车辆详情
    viewCarDetail(e) {
        const carId = e.currentTarget.dataset.id;
        wx.navigateTo({
            url: `/pages/car/detail/index?id=${carId}`
        });
    },

    // 下拉刷新
    async onPullDownRefresh() {
        // 下拉刷新时显示骨架屏
        this.setData({
            isLoading: true,
            showLoadingAnimation: false // 改为false显示骨架屏
        });

        // 检查登录状态
        this.checkLoginStatus();

        // 重新获取推荐车辆
        await this.getRecommendCars();

        // 设置短暂延时后关闭加载状态
        setTimeout(() => {
            this.setData({
                isLoading: false
            });
            wx.stopPullDownRefresh();
        }, 800); // 增加延时时间，与页面加载保持一致
    },

    // 跳转到企业认证页面
    goCertification() {
        if (!this.data.hasUserInfo) {
            wx.showToast({
                title: '请先登录',
                icon: 'none',
                duration: 2000
            });

            setTimeout(() => {
                wx.navigateTo({
                    url: '/pages/login/index'
                });
            }, 1000);
            return;
        }

        wx.navigateTo({
            url: '/pages/info/certification'
        });
    },
    // 跳转到车辆评估页面
    goEvaluate() {
        wx.navigateTo({
            url: '/pages/evaluate/index'
        });
    },

    // 添加去看看车按钮的点击事件处理方法
    onGoToCarList(e) {
        // 获取点击的item标识
        const itemType = e.currentTarget.dataset.type;

        if (itemType === 'certification') {
            // 跳转到商户认证页面
            wx.navigateTo({
                url: '/pages/info/certification'
            });
        } else if (itemType === 'cs') {
            // 显示客服弹窗
            this.showCustomerServicePopup();
        } else {
            // 默认行为，跳转到车辆列表
            wx.switchTab({
                url: '/pages/buy/index'
            });
        }
    },

    onGoToQuteRecordList() {
        wx.navigateTo({
            url: '/pages/my/quote_list'
        })
    },

    // 跳转到报价单页面
    navigateToQuote() {
        wx.navigateTo({
            url: '/pages/my/quote'
        });
    },

    // 跳转到报价单页面
    navigateToArticle() {
        wx.navigateTo({
            url: '/pages/article/index'
        });
    },

    navigateToSettings() {
        wx.navigateTo({
            url: '/pages/set/index'
        });
    },

    // 跳转到会员中心页面
    navigateToMember() {
        wx.navigateTo({
            url: '/pages/member/index'
        });
    },

    onGoToFavorite() {
        wx.navigateTo({
            url: '/pages/favorite/index'
        });
    },

    // 点击"更多好车"
    onMoreCarsTap() {
        wx.navigateTo({
            url: '/pages/buy/index'
        });
    },
    onGoToFeedback() {
        wx.navigateTo({
            url: '/pages/feedback/index'
        });
    },

    onGoToTrainOperationList() {
        wx.navigateTo({
            url: '/pages/train_operation/index'
        });
    },

    onGoToOrderList() {
      wx.navigateTo({
          url: '/pages/my/order'
      });
  },

    // 显示登录弹窗
    showLoginPopup(e) {
        // 获取点击的功能名称
        const feature = e.currentTarget.dataset.feature || '此功能';

        // 设置登录弹窗选项
        this.setData({
            loginPopupOptions: {
                title: `登录后即可使用${feature}`,
                buttonText: '立即登录',
                imageUrl: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274411926_900.png'
            },
            showLoginPopup: true
        });
    },

    // 关闭登录弹窗
    closeLoginPopup() {
        this.setData({
            showLoginPopup: false
        });
    },

    // 处理登录按钮点击
    handleLogin() {
        // 隐藏弹窗
        this.setData({
            showLoginPopup: false
        });

        // 跳转到登录页面
        wx.navigateTo({
            url: '/pages/login/index'
        });
    },

    // 导航到个人信息页面
    navigateToUserProfile() {
        wx.navigateTo({
            url: '/pages/set/revise'
        });
    },

    goVideo() {
        wx.navigateTo({
            url: '/pages/video/index'
        });
    },

    // 客服弹窗方法
    showCustomerServicePopup() {
        this.setData({
            showCustomerServicePopup: true
        });
    },

    // 关闭客服弹窗
    closeCustomerServicePopup() {
        this.setData({
            showCustomerServicePopup: false,
            showOfficialServicePopup: false
        });
    },

    // 显示官方客服QR码
    showOfficialServiceQR() {
        this.setData({
            showCustomerServicePopup: false, // 关闭第一个弹窗
            showOfficialServicePopup: true   // 显示QR码弹窗
        });
    },

    // 拨打客服电话
    callCustomerService() {
        wx.makePhoneCall({
            phoneNumber: this.data.customerServicePhone,
            fail: (err) => {
                wx.showToast({
                    title: '拨打电话失败',
                    icon: 'none'
                });
            }
        });
    },
    
    // 处理QR码加载错误
    qrCodeError(e) {
        console.error('QR码加载失败:', e);
        wx.showToast({
            title: '二维码加载失败',
            icon: 'none'
        });
    },
    
    // 保存QR码到相册
    saveQRCodeToAlbum() {
        wx.showLoading({
            title: '保存中...',
        });
        
        // 获取当前QR码的临时路径
        wx.getImageInfo({
            src: this.data.qrCodeUrl,
            success: (res) => {
                // 保存图片到相册
                wx.saveImageToPhotosAlbum({
                    filePath: res.path,
                    success: () => {
                        wx.hideLoading();
                        wx.showToast({
                            title: '已保存到相册',
                            icon: 'success'
                        });
                    },
                    fail: (err) => {
                        wx.hideLoading();
                        wx.showToast({
                            title: '保存失败',
                            icon: 'none'
                        });
                        console.error('保存失败:', err);
                    }
                });
            },
            fail: (err) => {
                wx.hideLoading();
                wx.showToast({
                    title: '获取图片失败',
                    icon: 'none'
                });
                console.error('获取图片失败:', err);
            }
        });
    },
    
    // 打开企业微信客服聊天
    openCustomerServiceChat() {
        wx.openCustomerServiceChat({
            extInfo: {url: 'https://work.weixin.qq.com/kfid/kfc4fa17a0f88a6e7c8'},
            corpId: 'ww3f7ad32ebbae2192', // 这里需要填写您的企业微信ID
            success(res) {
                console.log('打开客服聊天窗口成功', res);
            },
            fail(err) {
                console.error('打开客服聊天窗口失败', err);
                wx.showToast({
                    title: '打开客服聊天失败',
                    icon: 'none'
                });
            }
        });
    }
});