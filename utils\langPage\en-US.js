const message = {
  'zhaochexia':'ZhaoChe<PERSON><PERSON>',
  'login':'Login',
  'sign_up':'Sign up',
  'languageSwitchSuccess': 'Switched',
  'i_agree_to_abide_by':'I agree to abide by',
  'user_service_agreement':'User Service Agreement',
  'privacy_policy':'Privacy Policy',
  'please_enter_the_verification_code':'Enter code',
  'get_phone_code':'Get phone code',
  's_to_retry':'s to retry',
  'enter_phone_number':'enter phone number',
  'code_sent':'Code sent',
  'phone_already_used':'Phone already used',
  'send_failed':'Send failed',
  'invalid_number':'invalid number',
  'not_registered':'Not registered',
  'install_sms_plugin':'Install SMS Plugin',
  'send_failed_check_config':'Send failed, check config',
  'enter_password':'Enter password',
  'pass_chars':'8-16 chars',
  'forgot_password':'Forgot password',
  'enter_phone_or_username':'Enter phone or username',
  'phone_login':'Phone login',
  'enter_phone_or_username':'Enter Phone or username',
  'please_log_in':'Please log in',
  'quick_login':'Quick login',
  'login_with_password':'Login with password',
  'confirm_new_password':'Confirm new password',
  'passwords_dont_match':'Passwords don’t match',
  'password_updated':'Password updated',
  'reset_password':'Reset password',
  'agree_to_terms_first':'Agree to terms first',
  'authorizing':'Authorizing',
}

function trans(key){
  return message[key] || key;
}

module.exports = {
  trans
};