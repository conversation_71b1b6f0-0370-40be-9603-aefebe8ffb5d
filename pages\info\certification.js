// pages/info/certification.js
import api from '../../utils/api';
import util from '../../utils/util';
import config from '../../config';  // 假设配置文件在这个位置
import cosUpload from '../../utils/cos-upload';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,  // 状态栏高度
    isAgree: false,  // 是否同意协议
    licenseImage: '',  // 营业执照图片
    handbookImage: '', // 企业手册图片
    pdfPath: '',      // PDF文件路径
    pdfName: '',      // PDF文件名
    pdfSize: '',      // PDF文件大小
    companyName: null,   // 公司名称
    address: null,       // 地址
    registeredCapital: null, // 注册资金
    companyIntro: null,    // 企业介绍
    brandInfo: null,        // 主营品牌和车型
    userInfo: null,        // 用户信息
    isFirstLoad: true     // 标记首次加载
  },

  // 添加基础URL常量
  // baseUrl: 'https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/uploads/',
  baseUrl: config.COS_CONFIG.url + config.COS_CONFIG.path,

  // 获取文件路径后缀（用于提交）
  getFilePathSuffix(fullPath) {
    if (!fullPath) return '';
    // 检查是否已经包含完整路径
    if (fullPath.startsWith('http')) {
      // 从URL中提取路径
      const parts = fullPath.split(this.baseUrl);
      if (parts.length > 1) {
        // 确保路径以 /uploads 开头
        return '/uploads/' + parts[1];
      }
      return '';
    } else if (fullPath.includes('uploads/')) {
      // 如果已经包含 uploads/ 但不是完整URL
      const parts = fullPath.split('uploads/');
      return '/uploads/' + parts[1];
    } else {
      // 如果是相对路径，直接添加前缀
      return '/uploads/' + fullPath;
    }
  },

  // 获取完整文件URL（用于显示）
  getFullFileUrl(pathSuffix) {
    if (!pathSuffix) return '';
    // 移除开头的 /uploads 如果存在
    const cleanPath = pathSuffix.startsWith('/uploads/')
      ? pathSuffix.substring(9)
      : (pathSuffix.startsWith('uploads/') ? pathSuffix.substring(8) : pathSuffix);
    return this.baseUrl + cleanPath;
  },

  observers: {
    'companyName, address, registeredCapital, licenseImage, handbookImage, companyIntro, brandInfo': function (
      companyName,
      address,
      registeredCapital,
      licenseImage,
      handbookImage,
      companyIntro,
      brandInfo
    ) {
      console.log('数据变化：', {
        companyName,
        address,
        registeredCapital,
        licenseImage,
        handbookImage,
        companyIntro,
        brandInfo
      });
    }
  },

  // 返回上一页
  navigateBack() {
    console.log('点击返回按钮');
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    console.log('状态栏高度:', systemInfo.statusBarHeight);
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight || 20
    });

    this.data.isFirstLoad = true;  // 标记首次加载
    // console.log('页面加载开始');
    const userInfo = util.getUserInfo();
    // console.log('获取到的用户信息：', userInfo);
    // 先设置userInfo，确保页面有初始数据
    this.setData({ userInfo }, () => {
      console.log('初始userInfo设置完成');
    });

    if (userInfo && userInfo.app_id) {
      // console.log('开始获取认证信息，app_id:', userInfo.app_id);
      // 显示加载中
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      // 获取认证信息
      api.user.getAuthInfo({ app_id: userInfo.app_id })
        .then(result => {
          wx.hideLoading();
          this.data.isFirstLoad = false;  // 重置首次加载标记
          // console.log('认证信息接口返回：', result);

          if (result) {
            const authInfo = result;
            // console.log('准备设置的认证信息：', authInfo);

            setTimeout(() => {
              this.setData({
                companyName: authInfo.company_name || '',
                address: authInfo.address || '',
                registeredCapital: authInfo.registration_fee || '',
                licenseImage: this.getFullFileUrl(authInfo.business_license),
                handbookImage: this.getFullFileUrl(authInfo.handbookImage),
                companyIntro: authInfo.introduce || '',
                brandInfo: authInfo.brandInfo || '',
                pdfPath: this.getFullFileUrl(authInfo.pdfFile?.path),
                pdfName: authInfo.pdfFile?.name || '',
                pdfSize: authInfo.pdfFile?.size || ''
              });
            }, 100);

            // console.log('设置后的页面数据：', this.data);

            // 如果已经认证通过，显示提示
            // if (authInfo.is_auth === 2) {
            //   wx.showToast({
            //     title: '您的企业已认证通过',
            //     icon: 'success',
            //     duration: 2000
            //   });
            // }
            // // 如果正在审核中
            // else if (authInfo.is_auth === 1) {
            //   wx.showToast({
            //     title: '认证信息审核中',
            //     icon: 'none',
            //     duration: 2000
            //   });
            // }
          }
        })
        .catch(error => {
          wx.hideLoading();
          this.data.isFirstLoad = false;  // 重置首次加载标记
          console.error('获取认证信息失败:', error);
          wx.showToast({
            title: '获取认证信息失败',
            icon: 'none',
            duration: 2000
          });
        });
    } else {
      // console.log('未获取到用户信息或app_id');
      this.setData({ userInfo }); // 如果没有app_id，也要设置userInfo
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 确保状态栏高度正确设置
    if (!this.data.statusBarHeight) {
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight || 20
      });
    }

    // 页面显示时也尝试获取数据
    const userInfo = util.getUserInfo();
    if (userInfo && userInfo.app_id && !this.data.companyName && !this.data.isFirstLoad) {
      this.fetchAuthInfo(userInfo.app_id);
    }
    // 检查数据加载状态
    this.checkDataStatus();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },



  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 显示加载提示
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        // 上传图片到腾讯云COS
        cosUpload.uploadFile(res.tempFilePaths[0], 'license')
          .then(result => {
            this.setData({
              licenseImage: this.getFullFileUrl(this.getFilePathSuffix(result.url))
            });
            wx.hideLoading();
          })
          .catch(error => {
            console.error('上传失败:', error);
            wx.hideLoading();
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            });
          });
      }
    });
  },

  // 删除图片
  deleteLicenseImage() {
    this.setData({
      licenseImage: ''
    });
  },

  // 输入事件处理
  onCompanyNameInput(e) {
    // console.log('公司名称输入：', e.detail.value);
    this.setData({
      companyName: e.detail.value
    });
  },

  onAddressInput(e) {
    // console.log('地址输入：', e.detail.value);
    this.setData({
      address: e.detail.value
    });
  },

  onRegisteredCapitalInput(e) {
    // console.log('注册资金输入：', e.detail.value);
    this.setData({
      registeredCapital: e.detail.value
    });
  },



  // 选择企业手册图片
  chooseHandbookImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 显示加载提示
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        // 上传图片到腾讯云COS
        cosUpload.uploadFile(res.tempFilePaths[0], 'handbook')
          .then(result => {
            this.setData({
              handbookImage: this.getFullFileUrl(this.getFilePathSuffix(result.url))
            });
            wx.hideLoading();
          })
          .catch(error => {
            console.error('上传失败:', error);
            wx.hideLoading();
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            });
          });
      }
    });
  },

  // 删除企业手册图片
  deleteHandbookImage() {
    this.setData({
      handbookImage: ''
    });
  },

  // 选择PDF文件
  choosePdf() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        const file = res.tempFiles[0];

        // 检查文件类型
        if (!file.name.toLowerCase().endsWith('.pdf')) {
          wx.showToast({
            title: '请选择PDF文件',
            icon: 'none'
          });
          return;
        }

        // 显示加载提示
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        // 上传PDF到腾讯云COS
        cosUpload.uploadFile(file.path, 'pdf')
          .then(result => {
            this.setData({
              pdfPath: this.getFullFileUrl(this.getFilePathSuffix(result.url)),
              pdfName: file.name,
              pdfSize: (file.size / 1024 / 1024).toFixed(2) + 'MB'
            });
            wx.hideLoading();
          })
          .catch(error => {
            console.error('上传失败:', error);
            wx.hideLoading();
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            });
          });
      }
    });
  },

  // 删除PDF
  deletePdf() {
    this.setData({
      pdfPath: '',
      pdfName: '',
      pdfSize: ''
    });
  },

  // 预览PDF文件
  previewPdf() {
    if (!this.data.pdfPath) {
      wx.showToast({
        title: '没有可预览的PDF文件',
        icon: 'none'
      });
      return;
    }

    // 获取完整的PDF文件路径
    let fullPdfPath = '';
    if (this.data.pdfPath.startsWith('http')) {
      fullPdfPath = this.data.pdfPath;
    } else {
      // 移除开头的 /uploads 如果存在
      const cleanPath = this.data.pdfPath.startsWith('/uploads/')
        ? this.data.pdfPath.substring(9)
        : (this.data.pdfPath.startsWith('uploads/') ? this.data.pdfPath.substring(8) : this.data.pdfPath);
      fullPdfPath = this.baseUrl + cleanPath;
    }

    // console.log('预览PDF路径:', fullPdfPath);

    wx.showLoading({
      title: '加载中...',
    });

    // 先下载文件到本地临时路径
    wx.downloadFile({
      url: fullPdfPath,
      success: (res) => {
        if (res.statusCode === 200) {
          // 使用微信的文档预览API
          wx.openDocument({
            filePath: res.tempFilePath,
            fileType: 'pdf',
            success: function (res) {
              console.log('打开文档成功');
            },
            fail: function (error) {
              console.error('打开文档失败:', error);
              wx.showToast({
                title: '文档打开失败',
                icon: 'none'
              });
            }
          });
        } else {
          wx.showToast({
            title: '文件下载失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('下载文件失败:', error);
        wx.showToast({
          title: '文件下载失败',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },



  // 企业介绍输入处理
  onCompanyIntroInput(e) {
    this.setData({
      companyIntro: e.detail.value
    });
  },



  // 主营品牌输入处理
  onBrandInfoInput(e) {
    this.setData({
      brandInfo: e.detail.value
    });
  },

  // 切换协议同意状态
  toggleAgree() {
    this.setData({
      isAgree: !this.data.isAgree
    });
  },

  // 查看协议
  viewAgreement(e) {
    const type = e.currentTarget.dataset.type;
    let url = '';

    if (type === 'service') {
      url = '/pages/agreement/service';
    } else if (type === 'privacy') {
      url = '/pages/agreement/privacy';
    }

    wx.navigateTo({
      url: url
    });
  },

  // 提交信息
  submitInfo() {
    if (!this.data.isAgree) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      });
      return;
    }

    // 验证表单是否填写完整
    if (!this.data.licenseImage) {
      wx.showToast({
        title: '请上传营业执照',
        icon: 'none'
      });
      return;
    }

    if (!this.data.companyName) {
      wx.showToast({
        title: '请输入公司全称',
        icon: 'none'
      });
      return;
    }

    if (!this.data.address) {
      wx.showToast({
        title: '请输入公司地址',
        icon: 'none'
      });
      return;
    }

    if (!this.data.registeredCapital) {
      wx.showToast({
        title: '请输入注册金',
        icon: 'none'
      });
      return;
    }

    // 企业手册验证（可选，根据需求决定是否必填）
    if (!this.data.handbookImage) {
      wx.showToast({
        title: '请上传企业手册图片',
        icon: 'none'
      });
      return;
    }

    // 企业介绍验证
    if (!this.data.companyIntro) {
      wx.showToast({
        title: '请输入企业详细介绍',
        icon: 'none'
      });
      return;
    }

    // 主营品牌验证
    if (!this.data.brandInfo) {
      wx.showToast({
        title: '请输入主营品牌和车型',
        icon: 'none'
      });
      return;
    }

    // 收集表单数据
    const formData = {
      // 营业执照信息
      business_license: this.getFilePathSuffix(this.data.licenseImage), //只传后半段路径
      company_name: this.data.companyName,//公司全称
      address: this.data.address,//地址
      registration_fee: this.data.registeredCapital,//注册金

      // 企业手册信息
      handbookImage: this.getFilePathSuffix(this.data.handbookImage),
      pdfFile: {
        path: this.getFilePathSuffix(this.data.pdfPath),
        name: this.data.pdfName,
        size: this.data.pdfSize
      },

      // 企业详细介绍
      introduce: this.data.companyIntro, //企业介绍

      // 主营品牌和车型
      brandInfo: this.data.brandInfo,

      // 获取用户app_id
      app_id: util.getUserInfo()?.app_id || ''
    };

    // 在控制台打印表单数据
    // console.log('表单数据：', formData);

    // 显示加载提示
    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 提交数据到服务器
    api.user.submitCertification(formData)
      .then(result => {
        wx.hideLoading();

        // 更新本地用户信息，标记为已认证
        const userInfo = util.getUserInfo();
        if (userInfo) {
          userInfo.is_auth = 1; // 标记为已提交认证
          util.setCacheWithExpiry('userInfo', userInfo);
        }

        // 提示用户提交成功
        wx.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟跳转到认证审核中页面
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/info/certification_review'
          });
        }, 2000);
      })
      .catch(error => {
        wx.hideLoading();
        console.error('认证提交失败:', error);
        wx.showToast({
          title: error.message || '提交失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 抽取获取认证信息的方法
  fetchAuthInfo(app_id) {
    api.user.getAuthInfo({ app_id })
      .then(result => {
        if (result.code === 0 && result.data) {
          const authInfo = result.data;
          this.setData({
            companyName: authInfo.company_name || '',
            address: authInfo.address || '',
            registeredCapital: authInfo.registration_fee || '',
            // ... 其他数据设置
          });
        }
      });
  },

  // 检查数据加载状态
  checkDataStatus() {
    console.log('当前页面数据状态：', {
      companyName: this.data.companyName,
      address: this.data.address,
      registeredCapital: this.data.registeredCapital,
      licenseImage: this.data.licenseImage,
      handbookImage: this.data.handbookImage,
      companyIntro: this.data.companyIntro,
      brandInfo: this.data.brandInfo
    });
  }
})