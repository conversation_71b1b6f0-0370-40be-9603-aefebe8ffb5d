<view
  class="container"
  style="--status-bar-height: {{statusBarHeight}}px;"
>
  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px;"
  >
    <view class="nav-content">
      <view
        class="back-icon"
        bindtap="navigateBack"
      >
        <image
          src="/icons/moments/back.svg"
          mode="aspectFit"
        ></image>
      </view>
      <view class="nav-title">国内拖车服务</view>
    </view>
  </view>

  <!-- 主内容区域 -->
  <view
    class="main-content"
    style="padding-top: {{statusBarHeight + 50}}px;"
  >

    <!-- 顶部区域：固定高度容器，用于容纳搜索框或筛选标题 -->
    <view class="fixed-header">
      <!-- 自定义导航栏 -->
      <view class="custom-nav">
        <view
          class="status-bar"
          style="height: {{statusBarHeight}}px;"
        ></view>
        <view class="nav-title">
          <view style="display: flex;">
            <view
              bindtap="navigateBack"
              style="margin-left:10rpx;"
            >
              <van-icon
                name="arrow-left"
                size="20px"
                color="#333"
              />
            </view>
            <view style="text-align: center;width: 90%;">检查服务</view>
          </view>
        </view>
      </view>
      <view
        class="top-container"
        style="height: {{ topContainerHeight }}px;"
      >
        <!-- 搜索区域 -->
        <view class="search-bar">
          <view class="search-input-wrapper">
            <icon
              type="search"
              size="14"
              class="search-icon"
            ></icon>
            <input
              class="search-input"
              placeholder="请输入类型或公司名称"
              placeholder-style="color: #aaa; font-size: 26rpx;"
              value="{{ searchValue || ''}}"
              bindinput="onSearchInput"
              bindconfirm="onSearch"
            />
            <view
              class="search-btn"
              bindtap="onSearch"
            >搜索</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 内容区域 - 添加一个占位符，高度等于固定头部的高度 -->
    <view class="header-placeholder"></view>



    <!-- 车辆列表(使用scroll-view替换普通view) -->
    <scroll-view
      class="vehicle-list"
      scroll-y="true"
      bindscrolltolower="onScrollToLower"
      lower-threshold="100"
      enable-back-to-top="true"
      refresher-enabled="{{false}}"
      style="height: calc(100vh - {{statusBarHeight + 44}}px - 90rpx - 60rpx);"
    >
      <view
        wx:for="{{vehicleList}}"
        wx:key="id"
        class="vehicle-item"
        data-id="{{item.id}}"
        bindtap="tapItem"
      >
        <!-- 车型信息和右箭头 -->
        <view class="vehicle-name-row">
          <view class="vehicle-name">{{item.company_name}}</view>
          <!-- 右箭头 -->
          <view class="arrow">
            <image
              src="/icons/arrow-right.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>

        <!-- 详细信息区块 -->
        <view class="vehicle-info">
          <view class="info-header">
            <text>类型</text>
            <text>拖车类型</text>
            <text>费用（元）</text>
          </view>
        </view>

        <view class="vehicle-info">
          <view class="info-header">
            <text class="info-text">{{item.classes}}</text>
            <text class="info-text">{{item.trailer_type}}</text>
            <text class="info-text">{{item.charge}}</text>
          </view>
        </view>
        <!-- 发布时间 -->
        <view class="publish-time">出发点 <text class="info-text">{{item.province || ''}}</text></view>
        <view class="publish-time">可到达地 <text class="info-text">{{item.city || ''}}</text></view>
      </view>

      <!-- 没有数据时显示的提示 -->
      <view
        class="empty-tip"
        wx:if="{{vehicleList.length === 0}}"
      >
        暂无数据
      </view>

      <!-- 加载更多提示 -->
      <view
        class="loading-more"
        wx:if="{{vehicleList.length > 0}}"
      >
        <view
          wx:if="{{isLoading}}"
          class="loading"
        >
          <view class="loading-icon"></view>
          <text>加载中...</text>
        </view>
        <view
          wx:elif="{{!hasMore}}"
          class="no-more"
        >
          <text>—— 已经到底了 ——</text>
        </view>
        <view
          wx:else
          class="pull-tip"
        >
          <text>上拉加载更多</text>
        </view>
      </view>

      <!-- 添加手动加载更多按钮 -->
      <view
        wx:if="{{hasMore && !isLoading && vehicleList.length > 0}}"
        class="load-more-btn"
        bindtap="loadMore"
      >
        点击加载更多
      </view>
    </scroll-view>
  </view>
</view>