{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "automobile_trade", "setting": {"compileHotReLoad": true, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false}, "libVersion": "3.8.10", "condition": {"miniprogram": {"list": [{"name": "pages/info/certification_review", "pathName": "pages/info/certification_review", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/info/index", "pathName": "pages/info/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/supply_chain_services/car_service_detail", "pathName": "pages/supply_chain_services/car_service_detail", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/train_operation/details", "pathName": "pages/train_operation/detais", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/member/renewal", "pathName": "pages/member/renewal", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/vehicle/params", "pathName": "pages/vehicle/params", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/set/index", "pathName": "pages/set/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/buy/detail", "pathName": "pages/buy/detail", "query": "", "launchMode": "default", "scene": null}]}}}