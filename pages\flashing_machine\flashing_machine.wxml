<!--pages/train_operation/index.wxml-->
<view
  class="container"
  style="--status-bar-height: {{statusBarHeight}}px;"
>
  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px;"
  >
    <view class="nav-content">
      <view
        class="back-icon"
        bindtap="navigateBack"
      >
        <image
          src="/icons/moments/back.svg"
          mode="aspectFit"
        ></image>
      </view>
      <view class="nav-title">刷机服务</view>
    </view>
  </view>

  <!-- 主内容区域 -->
  <view
    class="main-content"
    style="padding-top: {{statusBarHeight + 50}}px;"
  >
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view
        wx:for="{{filterTypes}}"
        wx:key="index"
        class="filter-item {{currentFilter === index ? 'active' : ''}}"
        data-index="{{index}}"
        bindtap="tapFilter"
      >
        <text>{{item}}</text>
        <block>
          <!-- 全部品牌和订单类型图标 -->
          <image
            src="/icons/common/down.svg"
            mode="aspectFit"
            class="filter-icon {{currentFilter === index ? 'active' : ''}}"
          ></image>
        </block>
      </view>
    </view>

    <!-- 筛选面板 -->
    <view
      class="filter-panel"
      wx:if="{{showFilterPanel}}"
    >
      <!-- 品牌筛选面板 -->
      <scroll-view
        class="filter-panel-content"
        scroll-y
        wx:if="{{currentFilter === 0}}"
      >
        <view class="filter-title">选择品牌</view>
        <view class="brand-list">
          <view
            wx:for="{{brandList}}"
            wx:key="id"
            class="brand-item {{selectedBrand === item.id ? 'active' : ''}}"
            data-id="{{item.id}}"
            data-name="{{item.brand_name}}"
            bindtap="selectBrand"
          >
            <text>{{item.brand_name}}</text>
          </view>
        </view>
      </scroll-view>

      <!-- 订单类型筛选面板 -->
      <view
        class="filter-panel-content"
        wx:if="{{currentFilter === 1}}"
      >
        <view class="filter-title">选择语言</view>
        <view class="order-type-list">
          <view
            wx:for="{{LangList}}"
            wx:key="id"
            class="order-type-item {{LangListType === item.id ? 'active' : ''}}"
            data-id="{{item.id}}"
            bindtap="selectLangType"
          >
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>

      <!-- 是否远程筛选面板 -->
      <view
        class="filter-panel-content"
        wx:if="{{currentFilter === 2}}"
      >
        <view class="filter-title">是否远程</view>
        <view class="order-type-list">
          <view
            wx:for="{{onlineStatus}}"
            wx:key="id"
            class="order-type-item {{onlineStatusType === item.id ? 'active' : ''}}"
            data-id="{{item.id}}"
            bindtap="selectOnline"
          >
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>



    <!-- 车辆列表(使用scroll-view替换普通view) -->
    <scroll-view
      class="vehicle-list"
      scroll-y="true"
      bindscrolltolower="onScrollToLower"
      lower-threshold="100"
      enable-back-to-top="true"
      refresher-enabled="{{false}}"
      style="height: calc(100vh - {{statusBarHeight + 44}}px - 90rpx - 60rpx);"
    >
      <view
        wx:for="{{vehicleList}}"
        wx:key="id"
        class="vehicle-item"
        data-id="{{item.id}}"
        bindtap="tapItem"
      >
        <!-- 车型信息和右箭头 -->
        <view class="vehicle-name-row">
          <view class="vehicle-name">{{item.ui_vehicle_name}}</view>
          <!-- 右箭头 -->
          <view class="arrow">
            <image
              src="/icons/arrow-right.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>

        <!-- 详细信息区块 -->
        <view class="vehicle-info">
          <view class="content-info">
            <view class="content-item">
              <text class="content-item-title">品牌</text>
              <text class="content-item-content">{{item.brand_name || '-'}}</text>
            </view>
            <view class="content-item">
              <view class="content-item-content">
                <text class="content-item-title">车系</text> {{item.series_name || '-'}}
              </view>
            </view>
          </view>
          <view class="content-info">
            <view class="content-item">
              <text class="content-item-title">可否支持远程刷机</text>
              <text class="content-item-content">{{item.onlineText || '-'}}</text>
            </view>
            <view class="content-item">
              <view class="content-item-content">
                <text class="content-item-title">费用</text> {{item.cost || '-'}}
              </view>
            </view>
          </view>
          <view class="content-info">
            <view class="content-item">
              <text class="content-item-title">可刷语言</text>
              <text class="content-item-content">{{item.languageText || '-'}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 没有数据时显示的提示 -->
      <view
        class="empty-tip"
        wx:if="{{vehicleList.length === 0}}"
      >
        暂无数据
      </view>

      <!-- 加载更多提示 -->
      <view
        class="loading-more"
        wx:if="{{vehicleList.length > 0}}"
      >
        <view
          wx:if="{{isLoading}}"
          class="loading"
        >
          <view class="loading-icon"></view>
          <text>加载中...</text>
        </view>
        <view
          wx:elif="{{!hasMore}}"
          class="no-more"
        >
          <text>—— 已经到底了 ——</text>
        </view>
        <view
          wx:else
          class="pull-tip"
        >
          <text>上拉加载更多</text>
        </view>
      </view>

      <!-- 添加手动加载更多按钮 -->
      <view
        wx:if="{{hasMore && !isLoading && vehicleList.length > 0}}"
        class="load-more-btn"
        bindtap="loadMore"
      >
        点击加载更多
      </view>
    </scroll-view>
  </view>
</view>