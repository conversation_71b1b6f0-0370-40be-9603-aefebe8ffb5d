//语言列表
const message = {
  'zhaochexia':'找车侠',
  'login':'登录',
  'sign_up':'注册',
  'languageSwitchSuccess': '切换成功',
  'i_agree_to_abide_by':'我同意遵守',
  'user_service_agreement':'用户服务协议',
  'privacy_policy':'隐私政策',
  'please_enter_the_verification_code':'请输入验证码',
  'get_phone_code':'获取手机验证码',
  's_to_retry':'秒后重新获取',
  'enter_phone_number':'请输入手机号码',
  'code_sent':'验证码发送成功',
  'phone_already_used':'手机号已经被注册',
  'send_failed':'验证码发送失败',
  'invalid_number':'请输入正确的手机号',
  'not_registered':'手机号未注册',
  'install_sms_plugin':'请在后台插件管理安装短信验证插件',
  'send_failed_check_config':'发送失败，请检查短信配置是否正确',
  'enter_password':'请输入密码',
  'pass_chars':'密码由8-16位字符组成',
  'forgot_password':'忘记密码',
  'enter_phone_or_username':'请输入手机号/用户名',
  'phone_login':'手机登录',
  'please_log_in':'您还未登录，请登录后查看',
  'quick_login':'手机号快捷登录',
  'login_with_password':'账号密码登录',
  'confirm_new_password':'请确认新密码',
  'passwords_dont_match':'两次输入的密码不一致',
  'password_updated':'密码修改成功',
  'reset_password':'找回密码',
  'agree_to_terms_first':'请先同意服务协议',
  'authorizing':'授权中',
}

function trans(key){
  return message[key] || key;
}

module.exports = {
  trans
};