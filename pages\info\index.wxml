<view class="container">

  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px;"
  >
    <view class="nav-content">
      <!-- <view class="back-icon" bindtap="navigateBack">
        <image src="/icons/moments/back.svg" mode="aspectFit"></image>
      </view> -->
      <view class="nav-title">注册个人信息</view>
    </view>
  </view>

  <!-- 头像上传区域 -->
  <view class="avatar-upload">
    <button
      class="avatar-button"
      open-type="chooseAvatar"
      bindchooseavatar="onChooseAvatar"
    >
      <image
        class="avatar-image"
        src="{{avatarUrl || 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274246972_981.png'}}"
        mode="aspectFill"
      ></image>
      <view class="avatar-text">点击上传头像</view>
    </button>
  </view>

  <!-- 表单区域 -->
  <view class="form-section">
    <!-- 昵称 -->
    <view class="form-item">
      <text class="form-label required">名称</text>
      <view class="input-wrapper">
        <input
          class="form-input"
          placeholder="请输入公司名称"
          value="{{companyShortName}}"
          bindinput="onCompanyShortNameInput"
          type="nickname"
        />
      </view>
    </view>

    <!-- 用户类型 -->
    <view class="form-item">
      <text class="form-label required">请选择用户类型</text>
      <view class="user-type-grid">
        <view
          class="user-type-item {{userType === 1 ? 'active' : ''}}"
          bindtap="setUserType"
          data-type="1"
          hover-class="none"
        >
          <image
            class="type-icon"
            src="/icons/register/v.png"
            mode="aspectFit"
          ></image>
          <text>车源商</text>
        </view>
        <view
          class="user-type-item {{userType === 3 ? 'active' : ''}}"
          bindtap="setUserType"
          data-type="3"
          hover-class="none"
        >
          <image
            class="type-icon"
            src="/icons/register/a.png"
            mode="aspectFit"
          ></image>
          <text>汽配商</text>
        </view>
        <view
          class="user-type-item {{userType === 2 ? 'active' : ''}}"
          bindtap="setUserType"
          data-type="2"
          hover-class="none"
        >
          <image
            class="type-icon"
            src="/icons/register/s.png"
            mode="aspectFit"
          ></image>
          <text>服务商</text>
        </view>
        <view
          class="user-type-item {{userType === 4 ? 'active' : ''}}"
          bindtap="setUserType"
          data-type="4"
          hover-class="none"
        >
          <image
            class="type-icon"
            src="/icons/register/c.png"
            mode="aspectFit"
          ></image>
          <text>采购商</text>
        </view>
      </view>
    </view>

    <!-- 动态表单区域 - 使用固定高度容器 -->
    <view class="dynamic-form-container">

      <!-- 主营类型 - 只在汽配商类型下显示 -->
      <view class="form-item {{userType === 3 ? '' : 'hidden'}}">
        <text class="form-label required">主营类型（可多选）</text>
        <view
          class="car-type-new-container"
          style="{{vehicleTypeContainerStyle}}"
        >
          <view
            class="car-type-new-item {{item.isActive ? 'active' : ''}}"
            wx:for="{{businessTypeOptions}}"
            wx:key="value"
            data-type="{{item.name}}"
            data-value="{{item.value}}"
            data-index="{{index}}"
            bindtap="toggleBusinessType"
            style="width: 56px; height: 20px; line-height: 20px; display: flex; align-items: center; justify-content: center;"
          >
            {{item.name}}
          </view>
        </view>
      </view>


      <!-- 主营品牌 - 只在车源商、汽配商和采购商类型下显示 -->
      <view class="form-item {{userType === 1 || userType === 3 || userType === 4 ? '' : 'hidden'}}">
        <text class="form-label required">主营品牌（可多选）</text>
        <view
          class="dropdown-wrapper"
          bindtap="toggleBrandDropdown"
          hover-class="none"
        >
          <view class="dropdown-text {{!mainBrand ? 'dropdown-placeholder' : ''}}">
            {{mainBrand || '请选择您主营的车辆品牌'}}
          </view>
          <view class="dropdown-icon">
            <image
              src="/icons/register/dowm.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <!-- 下拉菜单 -->
        <block wx:if="{{showBrandDropdown}}">
          <view
            class="dropdown-overlay"
            bindtap="toggleBrandDropdown"
          ></view>
          <view class="dropdown-menu">
            <!-- 加载中提示 -->
            <view
              wx:if="{{isBrandLoading}}"
              class="loading-container"
            >
              <view class="loading-spinner"></view>
              <text class="loading-text">品牌加载中...</text>
            </view>
            <!-- 品牌列表 -->
            <block wx:else>
              <view
                wx:if="{{brandOptions.length === 0}}"
                class="empty-message"
              >
                暂无品牌数据
              </view>
              <view
                class="dropdown-item {{item.isActive ? 'active' : ''}}"
                wx:for="{{brandOptions}}"
                wx:key="value"
                bindtap="selectBrand"
                data-name="{{item.name}}"
                data-value="{{item.value}}"
                data-index="{{index}}"
              >
                {{item.name}}
                <view
                  wx:if="{{item.isActive}}"
                  class="dropdown-item-check"
                >✓</view>
              </view>
            </block>
          </view>
        </block>
      </view>



      <!-- 主营配件类目 - 只在汽配商类型下显示 -->
      <view class="form-item {{userType === 3 ? '' : 'hidden'}}">
        <text class="form-label required">主营配件类目</text>
        <view
          class="dropdown-wrapper"
          bindtap="togglePartsDropdown"
          hover-class="none"
        >
          <view class="dropdown-text {{!mainPartsCategory ? 'dropdown-placeholder' : ''}}">
            {{mainPartsCategory || '请选择您主营的配件类目'}}
          </view>
          <view class="dropdown-icon">
            <image
              src="/icons/register/dowm.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <!-- 修改为一级多选下拉菜单 -->
        <block wx:if="{{showPartsDropdown}}">
          <view
            class="dropdown-overlay"
            bindtap="togglePartsDropdown"
          ></view>
          <view class="dropdown-menu">
            <!-- 加载中提示 -->
            <view
              wx:if="{{isPartsLoading}}"
              class="loading-container"
            >
              <view class="loading-spinner"></view>
              <text class="loading-text">配件类目加载中...</text>
            </view>
            <!-- 配件类目列表 -->
            <block wx:else>
              <view
                wx:if="{{partsCategories.length === 0}}"
                class="empty-message"
              >
                暂无配件类目数据
              </view>
              <view
                class="dropdown-item {{item.isActive ? 'active' : ''}}"
                wx:for="{{partsCategories}}"
                wx:key="id"
                bindtap="selectPartCategory"
                data-id="{{item.id}}"
                data-name="{{item.name}}"
                data-index="{{index}}"
              >
                {{item.name}}
                <view
                  wx:if="{{item.isActive}}"
                  class="dropdown-item-check"
                >✓</view>
              </view>
            </block>
          </view>
        </block>
      </view>

      <!-- 主营业务 - 只在服务商类型下显示 -->
      <view class="form-item {{userType === 2 ? '' : 'hidden'}}">
        <text class="form-label required">主营业务（可多选）</text>
        <view
          class="dropdown-wrapper"
          bindtap="toggleBusinessDropdown"
          hover-class="none"
        >
          <view class="dropdown-text {{!mainBusiness ? 'dropdown-placeholder' : ''}}">
            {{mainBusiness || '请选择您主营的业务类型'}}
          </view>
          <view class="dropdown-icon">
            <image
              src="/icons/register/dowm.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <!-- 下拉菜单 -->
        <block wx:if="{{showBusinessDropdown}}">
          <view
            class="dropdown-overlay"
            bindtap="toggleBusinessDropdown"
          ></view>
          <view class="dropdown-menu">
            <view
              class="dropdown-item {{item.isActive ? 'active' : ''}}"
              wx:for="{{businessOptions}}"
              wx:key="value"
              bindtap="selectBusiness"
              data-name="{{item.name}}"
              data-value="{{item.value}}"
              data-index="{{index}}"
            >
              {{item.name}}
              <view
                wx:if="{{item.isActive}}"
                class="dropdown-item-check"
              >✓</view>
            </view>
          </view>
        </block>
      </view>

      <!-- 国家地区选择 - 只在采购商类型下显示 -->
      <view class="form-item {{userType === 4 ? '' : 'hidden'}}">
        <text class="form-label required">主营销售市场（可多选）</text>
        <view
          class="dropdown-wrapper"
          bindtap="toggleCountryRegionDropdown"
          hover-class="none"
        >
          <view class="dropdown-text {{!mainLocation ? 'dropdown-placeholder' : ''}}">
            {{mainLocation || '请选择您所在的国家'}}
          </view>
          <view class="dropdown-icon">
            <image
              src="/icons/register/dowm.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <!-- 国家多选下拉菜单 -->
        <block wx:if="{{showCountryRegionDropdown}}">
          <view
            class="dropdown-overlay"
            bindtap="toggleCountryRegionDropdown"
          ></view>
          <view class="dropdown-menu">
            <!-- 加载中提示 -->
            <view
              wx:if="{{isAreaLoading}}"
              class="loading-container"
            >
              <view class="loading-spinner"></view>
              <text class="loading-text">国家数据加载中...</text>
            </view>
            <!-- 国家列表 -->
            <block wx:else>
              <view
                wx:if="{{countryOptions.length === 0}}"
                class="empty-message"
              >
                暂无国家数据
              </view>
              <view
                class="dropdown-item {{item.isActive ? 'active' : ''}}"
                wx:for="{{countryOptions}}"
                wx:key="id"
                bindtap="selectCountry"
                data-name="{{item.name}}"
                data-value="{{item.id}}"
                data-index="{{index}}"
              >
                {{item.name}}
                <view
                  wx:if="{{item.isActive}}"
                  class="dropdown-item-check"
                >✓</view>
              </view>
            </block>
          </view>
        </block>
      </view>

      <!-- 主营车类型 - 只对车源商显示 -->
      <view class="form-item {{userType === 1 ? '' : 'hidden'}}">
        <text class="form-label required">主营车类型（可多选）</text>
        <view class="car-type-new-container">
          <block
            wx:for="{{vehicleTypeOptions}}"
            wx:key="value"
          >

            <view
              class="car-type-new-item {{item.isActive ? 'active' : ''}}"
              bindtap="toggleVehicleTypeNew"
              data-index="{{index}}"
            >
              {{item.name}}
            </view>
          </block>
        </view>
      </view>

      <!-- 车型类型占位 - 对其他用户类型 -->
      <view class="form-item {{userType !== 1 ? '' : 'hidden'}}">
        <text
          class="form-label"
          style="opacity: 0;"
        >占位</text>
        <view style="height: 80rpx; visibility: hidden;"></view>
      </view>
    </view>
  </view>

  <!-- 底部区域 - 不再固定 -->
  <view class="bottom-area">
    <!-- 提交按钮 -->
    <button
      class="submit-btn"
      bindtap="submitInfo"
    >完成注册</button>
  </view>
</view>