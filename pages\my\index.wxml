<view class="container">
  <!-- 引入登录弹窗模板 -->
  <import src="../../templates/loginPopup/loginPopup.wxml" />

  <!-- 骨架屏 - 仅在页面加载中且不显示正常加载动画时显示 -->
  <view
    class="skeleton-screen"
    wx:if="{{isLoading && !showLoadingAnimation}}"
  >
    <!-- 骨架导航栏 -->
    <view class="skeleton-nav">
      <view
        class="skeleton-status-bar"
        style="height: {{statusBarHeight}}px;"
      ></view>
      <view class="skeleton-nav-title"></view>
    </view>

    <!-- 骨架用户信息区域 -->
    <view class="skeleton-user-section">
      <view class="skeleton-user-card">
        <view class="skeleton-avatar"></view>
        <view class="skeleton-user-info">
          <view class="skeleton-username"></view>
          <view class="skeleton-meta"></view>
          <view class="skeleton-meta"></view>
        </view>
      </view>
    </view>

    <!-- 骨架功能菜单 -->
    <view class="skeleton-function-menu">
      <view class="skeleton-menu-row">
        <view class="skeleton-menu-item"></view>
        <view class="skeleton-menu-item"></view>
        <view class="skeleton-menu-item"></view>
        <view class="skeleton-menu-item"></view>
      </view>
    </view>

    <!-- 骨架快捷工具 -->
    <view class="skeleton-quick-tools">
      <view class="skeleton-tool-item"></view>
      <view class="skeleton-tool-item"></view>
    </view>

    <!-- 骨架工具菜单 -->
    <!-- <view class="skeleton-tool-menu">
      <view class="skeleton-tool-item"></view>
      <view class="skeleton-tool-item"></view>
      <view class="skeleton-tool-item"></view>
    </view> -->

    <!-- 骨架推荐区域 -->
    <view class="skeleton-recommend">
      <view class="skeleton-recommend-title"></view>
      <view class="skeleton-recommend-grid">
        <view class="skeleton-recommend-card"></view>
        <view class="skeleton-recommend-card"></view>
      </view>
    </view>
  </view>

  <!-- 原有加载状态 -->
  <view
    class="loading-container"
    wx:if="{{isLoading && showLoadingAnimation}}"
  >
    <view class="loading"></view>
  </view>

  <!-- 自定义导航栏 - 只有在非加载状态或者非骨架屏状态时显示 -->
  <view
    class="fixed-nav"
    wx:if="{{!isLoading || showLoadingAnimation}}"
  >
    <view
      class="status-bar"
      style="height: {{statusBarHeight}}px;"
    ></view>
    <view class="nav-title">我的</view>
  </view>

  <!-- 导航栏占位符，只有在非加载状态或者非骨架屏状态时显示 -->
  <view
    class="nav-placeholder"
    wx:if="{{!isLoading || showLoadingAnimation}}"
    style="height: {{statusBarHeight + 44}}px;"
  ></view>

  <!-- 页面内容 -->
  <view
    class="content"
    wx:if="{{!isLoading}}"
  >
    <!-- 用户信息区域 - 根据登录状态显示不同内容 -->
    <!-- 未登录状态 -->
    <view
      wx:if="{{!hasUserInfo}}"
      class="user-info-section"
    >
      <view class="user-info-card not-login-card">
        <view class="not-login-user-row">
          <image
            class="avatar-placeholder"
            src="{{loginPopupOptions.imageUrl || 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274411926_900.png'}}"
            mode="aspectFit"
          ></image>
          <button
            class="login-btn"
            bindtap="onLoginTap"
          >点击登录</button>
        </view>
      </view>
    </view>

    <!-- 已登录状态 -->
    <view
      wx:else
      class="user-info-section"
    >
      <view class="user-info-card">
        <view class="avatar-container">
          <image
            class="avatar"
            src="{{userInfo.logo || '/icons/my.png'}}"
            mode="aspectFit"
          ></image>
        </view>
        <view class="user-detail">
          <view class="username">{{userInfo.short_name || '尚品国际'}}</view>
          <view class="user-meta">
            <view class="meta-item">手机：{{userInfo.phone || '15558452254'}}</view>
            <view class="meta-item">角色：{{roleMap[userInfo.identity] || '总集商'}}</view>
          </view>
        </view>
        <view
          class="edit-profile-pill"
          bindtap="navigateToUserProfile"
        >
          信息修改
        </view>
      </view>
    </view>

    <!-- 功能菜单区 - 不再区分登录状态 -->
    <view class="function-menu">
      <view class="menu-box">
        <view class="menu-row">
          <view
            class="menu-item"
            bindtap="{{hasUserInfo ? 'onGoToCarList' : 'onLoginTap'}}"
            data-type="certification"
            data-feature="商户认证"
            wx:if="{{userInfo.account_type === '1'}}"
          >
            <image
              class="menu-icon"
              src="/icons/certification.png"
              mode="aspectFit"
            ></image>
            <text class="menu-text">商户认证</text>
          </view>
          <view
            class="menu-item"
            bindtap="{{hasUserInfo ? 'onGoToFavorite' : 'onLoginTap'}}"
            data-feature="我的收藏"
          >
            <image
              class="menu-icon"
              src="/icons/favorite.png"
              mode="aspectFit"
            ></image>
            <text class="menu-text">我的收藏</text>
          </view>
          <view
            class="menu-item"
            bindtap="{{hasUserInfo ? 'navigateToArticle' : 'onLoginTap'}}"
            data-feature="我的帖子"
          >
            <image
              class="menu-icon"
              src="/icons/article.png"
              mode="aspectFit"
            ></image>
            <text class="menu-text">我的帖子</text>
          </view>
          <view
            class="menu-item"
            bindtap="{{hasUserInfo ? 'navigateToMember' : 'onLoginTap'}}"
            data-feature="会员中心"
          >
            <image
              class="menu-icon"
              src="/icons/member.svg"
              mode="aspectFit"
            ></image>
            <text class="menu-text">会员中心</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷工具区 - 不再区分登录状态 -->
    <view class="quick-tools">
      <view class="quick-tools-row">
        <view
          class="quick-tool-item"
          bindtap="{{hasUserInfo ? 'navigateToQuote' : 'onLoginTap'}}"
          data-feature="生成报价单"
        >
          <view style="display: flex; align-items: center;">
            <image
              class="quick-tool-icon"
              src="/icons/quotation.png"
              mode="aspectFit"
            ></image>
            <text class="quick-tool-text">生成报价单</text>
          </view>
          <view class="arrow-icon"></view>
        </view>
        <view
          class="quick-tool-item"
          bindtap="{{hasUserInfo ? 'goVideo' : 'onLoginTap'}}"
          data-feature="视频"
        >
          <view style="display: flex; align-items: center;">
            <image
              class="quick-tool-icon"
              src="/icons/video/video.png"
              mode="aspectFit"
            ></image>
            <text class="quick-tool-text">视频</text>
          </view>
          <view class="arrow-icon"></view>
        </view>
      </view>
    </view>

    <!-- 工具菜单 - 不再区分登录状态 -->
    <view class="tool-menu">
      <view class="tool-menu-card">
        <!-- <view
          class="tool-item"
          bindtap="{{hasUserInfo ? 'goEvaluate' : 'showLoginPopup'}}"
          data-type="qr"
          data-feature="车价评估"
        >
          <image
            class="tool-icon"
            src="/icons/assess.png"
            mode="aspectFit"
          ></image>
          <text class="tool-text">车价评估</text>
          <view class="arrow-icon"></view>
        </view> -->
        <view
          class="tool-item"
          bindtap="{{hasUserInfo ? 'onGoToQuteRecordList' : 'onLoginTap'}}"
          data-type="qr"
          data-feature="报价单生成记录"
        >
          <image
            class="tool-icon"
            src="/icons/qute_record.png"
            mode="aspectFit"
          ></image>
          <text class="tool-text">报价单生成记录</text>
          <view class="arrow-icon"></view>
        </view>
        <view
          class="tool-item"
          bindtap="{{hasUserInfo ? 'onGoToTrainOperationList' : 'onLoginTap'}}"
          data-type="cs"
        >
          <image
            class="tool-icon"
            src="/icons/my/train_operation.svg"
            mode="aspectFit"
          ></image>
          <text class="tool-text">车务订单</text>
          <view class="arrow-icon"></view>
        </view>
        <!-- <view
          class="tool-item"
          bindtap="{{hasUserInfo ? 'onGoToOrderList' : 'onLoginTap'}}"
          data-type="cs"
        >
          <image
            class="tool-icon"
            src="/icons/my/train_operation.svg"
            mode="aspectFit"
          ></image>
          <text class="tool-text">支付订单</text>
          <view class="arrow-icon"></view>
        </view> -->
        <view
          class="tool-item"
          bindtap="showCustomerServicePopup"
          data-type="cs"
        >
          <image
            class="tool-icon"
            src="/icons/cs.png"
            mode="aspectFit"
          ></image>
          <text class="tool-text">联系客服</text>
          <view class="arrow-icon"></view>
        </view>
        <view
          class="tool-item"
          bindtap="onGoToCarList"
        >
          <image
            class="tool-icon"
            src="/icons/help.png"
            mode="aspectFit"
          ></image>
          <text class="tool-text">帮助中心</text>
          <view class="arrow-icon"></view>
        </view>
        <view
          class="tool-item"
          bindtap="onGoToCarList"
        >
          <image
            class="tool-icon"
            src="/icons/about.png"
            mode="aspectFit"
          ></image>
          <text class="tool-text">关于找车侠</text>
          <view class="arrow-icon"></view>
        </view>
        <view
          class="tool-item"
          bindtap="{{hasUserInfo ? 'onGoToFeedback' : 'onLoginTap'}}"
          data-feature="意见反馈"
        >
          <image
            class="tool-icon"
            src="/icons/my/feedback.png"
            mode="aspectFit"
          ></image>
          <text class="tool-text">意见反馈</text>
          <view class="arrow-icon"></view>
        </view>
        <view
          class="tool-item"
          bindtap="{{hasUserInfo ? 'navigateToSettings' : 'onLoginTap'}}"
          data-feature="账号设置"
        >
          <image
            class="tool-icon"
            src="/icons/account.png"
            mode="aspectFit"
          ></image>
          <text class="tool-text">设置</text>
          <view class="arrow-icon"></view>
        </view>

        <view
          class="tool-item"
          bindtap="{{hasUserInfo ? 'logout' : 'onLoginTap'}}"
          data-feature="退出账号"
          wx:if="{{hasUserInfo}}"
        >
          <image
            class="tool-icon"
            src="/icons/exit.png"
            mode="aspectFit"
          ></image>
          <text class="tool-text">退出账号</text>
          <view class="arrow-icon"></view>
        </view>
      </view>
    </view>

    <!-- 车辆推荐区域 -->
    <view
      class="recommend-section"
      wx:if="{{recommendCars.length > 0}}"
    >
      <view class="recommend-header">
        <text>好车推荐</text>
      </view>
      <view class="recommend-grid">
        <!-- 推荐车辆卡片 -->
        <view
          class="recommend-card"
          wx:for="{{recommendCars}}"
          wx:key="id"
          bindtap="onCarTap"
          data-id="{{item.id}}"
        >
          <!-- 卡片主体 -->
          <view class="recommend-card-body">
            <!-- 车辆图片 -->
            <image
              class="recommend-car-image"
              src="{{item.image}}"
              mode="aspectFill"
            ></image>

            <!-- 热销/新到标签 -->
            <view class="recommend-tag-label {{index % 2 === 0 ? 'hot' : 'new'}}">
              {{index % 2 === 0 ? '热销' : '新到'}}
            </view>

            <!-- 车辆标题 -->
            <view class="recommend-car-title">
              {{item.title}}
            </view>

            <!-- 车辆参数 -->
            <view class="recommend-car-params">
              <text>{{item.year}}年 | {{item.mileage}}万公里</text>
            </view>

            <!-- 价格信息 -->
            <view class="recommend-price-info">
              <text class="recommend-new-price">新车指导价{{item.originalPrice}}万元</text>
              <text class="recommend-sale-price">{{item.price}}万元</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 引用登录弹窗模板 -->
  <template
    is="loginPopup"
    data="{{ showLoginPopup, loginPopupOptions }}"
  />

  <!-- 原有客服弹窗 -->
  <view class="customer-service-popup" wx:if="{{showCustomerServicePopup}}">
    <view class="cs-popup-container">
      <!-- 客服信息卡片 -->
      <view class="cs-card">
        <view class="cs-info-item" bindtap="openCustomerServiceChat">
          <van-icon name="service-o" size="24px" class="cs-icon" />
          <text class="cs-text">企业微信客服</text>
        </view>
        <view class="cs-info-item" bindtap="callCustomerService">
          <van-icon name="phone-o" size="24px" class="cs-phone-icon" />
          <text class="cs-text">客服电话 {{customerServicePhone}}</text>
        </view>
      </view>
      
      <!-- 取消按钮卡片 -->
      <view class="cs-cancel-card" bindtap="closeCustomerServicePopup">
        <text class="cs-cancel-text">取消</text>
      </view>
    </view>
  </view>
  
  <!-- 官方客服QR码弹窗 -->
  <view class="official-service-popup" wx:if="{{showOfficialServicePopup}}">
    <view class="qr-card">
      <view class="qr-title">官方客服</view>
      <view class="qr-subtitle">添加官方客服，专业解答您的购车疑问，助您轻松选车</view>
      <view class="qr-code-container">
        <image class="qr-code-image" src="{{qrCodeUrl || 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/covers/20250724/56c0b5663730579689d35d8d88909e50.png'}}" mode="aspectFit" show-menu-by-longpress="true" binderror="qrCodeError" bindtap="saveQRCodeToAlbum"></image>
      </view>
      <view class="qr-tip">长按二维码识别或保存至相册</view>
    </view>
    <!-- 圆形关闭按钮 -->
    <view class="round-close-btn" bindtap="closeCustomerServicePopup">
      <view class="close-icon">×</view>
    </view>
  </view>
  
  <!-- 一键客服浮动按钮 -->
  <!-- <view class="floating-service-btn" bindtap="openCustomerServiceChat">
    <image class="floating-service-icon" src="/icons/cs.png" mode="aspectFit"></image>
    <text class="floating-service-text">在线客服</text>
  </view> -->
</view>