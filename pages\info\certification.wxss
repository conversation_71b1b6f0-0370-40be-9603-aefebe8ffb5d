/* pages/info/certification.wxss */

/* 页面样式 */
page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: fixed;
  -webkit-overflow-scrolling: touch;
  background-color: #F5F5F5;
}

.container {
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
  width: 60rpx;
  height: 44px;
  z-index: 10;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 主内容区域 */
.main-content {
  width: 100%;
  min-height: calc(100vh - 44px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  padding: 30rpx;
  box-sizing: border-box;
}

.upload-section {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-header {
  padding: 30rpx;
  position: relative;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.title-row {
  display: flex;
  align-items: center;
}



.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 10rpx;

 
  
}

.arrow {
  position: absolute;
  right: 30rpx;
  top: 30%;
  transform: translateY(-50%);
  width: 20rpx;
  height: 20rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
}

.arrow.down {
  transform: translateY(-80%) rotate(45deg);
}

.arrow.up {
  transform: translateY(-20%) rotate(-135deg);
}

.section-content {
  padding: 30rpx;
  background: #fff;
  transition: all 0.3s ease;
  height: auto;
  overflow: hidden;
  border-radius: 0 0 24rpx 24rpx;
}



.upload-area {
  width: 100%;
  height: 300rpx;
  border: 2rpx dashed #e0e0e0;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  box-sizing: border-box;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #4080ff;
  background: #f0f7ff;
}

.upload-area:last-child {
  margin-bottom: 0;
}

.upload-icon {
  font-size: 60rpx;
  color: #999;
}

.upload-text {
  font-size: 28rpx;
  color: #999;
}

.image-preview {
  width: 100%;
  height: 300rpx;
  position: relative;
}

.image-preview image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0,0,0,0.5);
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.form-group {
}

/* 表单组样式 */
.form-group {
  border-top: 1rpx solid #f5f5f5;
}

/* 表单行布局 */
.form-row {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-row:last-child {
  border-bottom: none;
}

/* 左侧标签样式 */
.form-label-left {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
  position: relative;
}

/* 必填标记 */
.required-mark {
  color: #ff4757;
  font-size: 28rpx;
  margin-left: 4rpx;
}

/* 右侧输入框样式 */
.form-input-right {
  flex: 1;
  height: 60rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: none;
  background-color: transparent;
  color: #333;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.form-input-right::placeholder {
  color: #999;
  font-size: 28rpx;
}



/* 确保输入框内容可见 */
input, textarea {
  color: #333 !important;
  background-color: #fff !important;
}

/* PDF文件预览样式 */
.file-preview {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  box-sizing: border-box;
  border: 2rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.file-preview:hover {
  border-color: #4080ff;
  box-shadow: 0 2rpx 12rpx rgba(64, 128, 255, 0.15);
}

.pdf-icon {
  width: 80rpx;
  height: 80rpx;
  background: #4080ff;
  color: #fff;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 20rpx;
}

.pdf-info {
  flex: 1;
  overflow: hidden;
  padding-right: 20rpx;
}

.pdf-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
  word-break: break-all;
  line-height: 1.4;
}

.pdf-size {
  font-size: 24rpx;
  color: #999;
}

.pdf-content {
  display: flex;
  align-items: center;
  flex: 1;
  padding-right: 40rpx;  /* 为删除按钮留出空间 */
}

/* 多行文本框样式 */
.textarea {
  width: 100%;
  height: 300rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 2rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.textarea:focus {
  border-color: #4080ff;
  box-shadow: 0 2rpx 12rpx rgba(64, 128, 255, 0.15);
}

/* 字数统计样式 */
.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}


/* 协议同意区域 */
.agreement-section {
  display: flex;
  align-items: center;
  margin: 40rpx 0 30rpx 0;
  padding: 0 10rpx;
}

.checkbox {
  transform: scale(0.8);
  margin-right: 10rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #999;
}


.link {
  color: #1296db;
}

/* 提交按钮容器 */
.submit-container {
  width: 100%;
  margin-bottom: 40rpx;
}

/* 提交按钮样式 */
.submit-btn {
  width: 100% !important;
  height: 96rpx !important;
  line-height: 96rpx !important;
  background-color: #4080ff !important;
  color: #fff !important;
  font-size: 32rpx !important;
  border-radius: 24rpx !important;
  border: none !important;
  font-weight: 500 !important;
  letter-spacing: 2rpx !important;
  box-shadow: none !important;
}

.submit-btn[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
}

.submit-btn::after {
  border: none !important;
}

/* 确保内容区域在 iOS 底部安全区域上方 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .main-content {
    padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .main-content {
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }
}