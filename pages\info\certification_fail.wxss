/* pages/info/certification_fail.wxss */

/* 页面样式 */
page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: fixed;
  -webkit-overflow-scrolling: touch;
  background-color: #F5F5F5;
}

.container {
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: transparent;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 20rpx;
  position: relative;
}

.back-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.back-icon:active {
  opacity: 0.8;
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 主内容区域 */
.main-content {
  padding: 40rpx 30rpx 60rpx;
  height: calc(100vh - var(--status-bar-height, 0px) - 44px);
  overflow-y: auto;
  box-sizing: border-box;
}

/* 状态区域 */
.status-section {
  text-align: center;
  margin-bottom: 60rpx;
  padding: 60rpx 0;
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 68, 68, 0.1);
  border-radius: 50%;
  border: 4rpx solid rgba(255, 68, 68, 0.2);
}

.status-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff4444;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(255, 68, 68, 0.1);
}

.status-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 卡片通用样式 */
.reason-card,
.tips-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-left: 12rpx;
}

/* 失败原因卡片 */
.reason-content {
  margin-bottom: 20rpx;
}

.reason-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  padding: 20rpx;
  background: rgba(255, 68, 68, 0.05);
  border-radius: 12rpx;
  border-left: 6rpx solid #ff4444;
}

.reject-time {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.time-label {
  margin-right: 10rpx;
}

.time-value {
  color: #666;
  font-weight: 500;
}

/* 温馨提示卡片 */
.tips-content {
  padding-left: 10rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-dot {
  color: #1296db;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 12rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 操作按钮区域 */
.action-section {
  margin-top: 60rpx;
  padding: 0 10rpx;
}

.primary-btn,
.secondary-btn {
  width: 100% !important;
  height: 96rpx !important;
  line-height: 96rpx !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
  border: none !important;
  margin-bottom: 20rpx;
  border-radius: 24rpx !important;
  letter-spacing: 2rpx !important;
  box-shadow: none !important;
}

.primary-btn {
  background-color: #4080ff !important;
  color: #fff !important;
}

.primary-btn:active {
  opacity: 0.8;
}

.secondary-btn {
  background-color: #fff !important;
  color: #666 !important;
  border: 2rpx solid #e0e0e0 !important;
}

.secondary-btn:active {
  background-color: #f5f5f5 !important;
}

/* 按钮禁用状态 */
.primary-btn[disabled],
.secondary-btn[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.7;
}

/* 按钮文字样式 */
.primary-btn::after,
.secondary-btn::after {
  border: none !important;
}

/* 响应式适配 */
@media (max-height: 600px) {
  .status-section {
    padding: 40rpx 0;
    margin-bottom: 40rpx;
  }

  .main-content {
    padding: 30rpx 30rpx 40rpx;
  }

  .action-section {
    margin-top: 40rpx;
  }
}