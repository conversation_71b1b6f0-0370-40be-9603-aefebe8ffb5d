<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px; --status-bar-height: {{statusBarHeight}}px;">
    <view class="status-bar"></view>
    <view class="nav-content">
      <view class="back-icon" bindtap="navigateBack">
        <image src="/icons/moments/back.svg" mode="aspectFit"></image>
      </view>
      <view class="nav-title">报价记录</view>
    </view>
  </view>

  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading && quotes.length === 0}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{isEmpty}}">
    <!-- <image class="empty-image" src="/icons/empty.png" mode="aspectFit"></image> -->
    <view class="empty-text">暂无报价记录</view>
  </view>

  <!-- 报价记录列表 -->
  <view class="quote-list-container" wx:else>
    <!-- 今日报价 -->
    <view class="date-group" wx:if="{{todayQuotes.length > 0}}">
      <view class="date-title">今日报价</view>
      <view class="quote-item" wx:for="{{todayQuotes}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
        <view class="quote-content">
          <view class="quote-title">{{item.vehicleName}}</view>
          <view class="divider-line"></view>
          <view class="quote-details">
            <view class="detail-row">
              <view class="detail-item">
                <text class="detail-label">厂商指导价</text>
                <text class="detail-value">{{item.formattedGuidancePrice}} 万元</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">市场优惠</text>
                <text class="detail-value red-text">{{item.formattedMarketDiscount}} 万元</text>
              </view>
            </view>
            <view class="detail-row">
              <view class="detail-item">
                <text class="detail-label">国内费用</text>
                <text class="detail-value">{{item.formattedDomesticFees}} 万元</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">出口费用</text>
                <text class="detail-value">{{item.formattedExportFees}} 万元</text>
              </view>
            </view>
          </view>
          <view class="quote-arrow">
            <image src="/icons/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 按日期分组的报价记录 -->
    <view class="date-group" wx:for="{{groupedQuotes}}" wx:key="date">
      <view class="date-title">{{item.date}}</view>
      <view class="quote-item" wx:for="{{item.quotes}}" wx:for-item="quote" wx:key="id" bindtap="navigateToDetail" data-id="{{quote.id}}">
        <view class="quote-content">
          <view class="quote-title">{{quote.vehicleName}}</view>
          <view class="divider-line"></view>
          <view class="quote-details">
            <view class="detail-row">
              <view class="detail-item">
                <text class="detail-label">厂商指导价</text>
                <text class="detail-value">{{quote.formattedGuidancePrice}} 万元</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">市场优惠</text>
                <text class="detail-value red-text">{{quote.formattedMarketDiscount}} 万元</text>
              </view>
            </view>
            <view class="detail-row">
              <view class="detail-item">
                <text class="detail-label">国内费用</text>
                <text class="detail-value">{{quote.formattedDomesticFees}} 万元</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">出口费用</text>
                <text class="detail-value">{{quote.formattedExportFees}} 万元</text>
              </view>
            </view>
          </view>
          <view class="quote-arrow">
            <image src="/icons/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多指示器 -->
    <view class="loading-more" wx:if="{{isLoading && quotes.length > 0}}">
      <view class="loading-spinner-small"></view>
      <text class="loading-text-small">加载更多...</text>
    </view>
    
    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && quotes.length > 0}}">
      <text>已经到底了</text>
    </view>
  </view>
</view>

