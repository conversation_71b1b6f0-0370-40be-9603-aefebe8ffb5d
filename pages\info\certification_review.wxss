/* pages/info/certification_review.wxss */

/* 页面样式 */
page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: fixed;
  -webkit-overflow-scrolling: touch;
  background-color: #F5F5F5;
}

.container {
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
  width: 60rpx;
  height: 44px;
  z-index: 10;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 主内容区域 */
.main-content {
  width: 100%;
  min-height: calc(100vh - 44px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  padding: 30rpx;
  box-sizing: border-box;
}

/* 审核状态卡片 */
.review-status-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 60rpx 30rpx 30rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  text-align: center;
  margin-top: 40rpx;
}

/* 机器人图标样式 */
.robot-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.robot-circle {
  width: 120rpx;
  height: 120rpx;
  background: #4a4a4a;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.robot-face {
  width: 80rpx;
  height: 60rpx;
  background: #fff;
  border-radius: 16rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.robot-eyes {
  display: flex;
  gap: 20rpx;
  margin-bottom: 8rpx;
}

.robot-eye {
  width: 8rpx;
  height: 8rpx;
  background: #333;
  border-radius: 50%;
}

.robot-mouth {
  width: 16rpx;
  height: 8rpx;
  background: #ff6b35;
  border-radius: 0 0 16rpx 16rpx;
}

.robot-headphones {
  position: absolute;
  top: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 140rpx;
  height: 140rpx;
}

.headphone-left,
.headphone-right {
  position: absolute;
  width: 24rpx;
  height: 32rpx;
  background: #ff6b35;
  border-radius: 12rpx;
  top: 40rpx;
}

.headphone-left {
  left: 10rpx;
}

.headphone-right {
  right: 10rpx;
}

.headphone-band {
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 100rpx;
  height: 4rpx;
  background: #ff6b35;
  border-radius: 2rpx;
}

/* 状态文字 */
.status-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

/* 描述文字 */
.status-desc {
  font-size: 28rpx;
  color: #999;
  line-height: 1.6;
}

.desc-line {
  margin-bottom: 8rpx;
}

.desc-line:last-child {
  margin-bottom: 0;
}

/* 分隔线 */
.divider {
  width: 100%;
  height: 1rpx;
  background: #f0f0f0;
  margin: 40rpx 0 30rpx 0;
}

/* 关注公众号区域 */

.follow-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
}

.follow-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.follow-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  background: #1296db;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.follow-info {
  flex: 1;
}

.follow-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.follow-subtitle {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.follow-btn {
  width: 120rpx !important;
  height: 60rpx !important;
  line-height: 60rpx !important;
  background-color: #07c160 !important;
  color: #fff !important;
  font-size: 28rpx !important;
  border-radius: 30rpx !important;
  border: none !important;
  font-weight: 500 !important;
}

.follow-btn::after {
  border: none !important;
}

/* 通用区域样式 */
.tips-section {
  margin-bottom: 30rpx;
}

/* 温馨提示卡片 */
.tips-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-item van-icon {
  margin-right: 15rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}

/* 撤回审核区域 */
.withdraw-section {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.withdraw-btn {
  width: 100% !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  background-color: #FF6B35 !important;
  color: #fff !important;
  font-size: 30rpx !important;
  border-radius: 24rpx !important;
  border: none !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4rpx 20rpx rgba(255, 107, 53, 0.3) !important;
}

.withdraw-btn::after {
  border: none !important;
}

.withdraw-btn:active {
  background-color: #E55A2B !important;
  transform: translateY(1rpx);
}

/* 确保内容区域在 iOS 底部安全区域上方 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .main-content {
    padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .main-content {
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }
}