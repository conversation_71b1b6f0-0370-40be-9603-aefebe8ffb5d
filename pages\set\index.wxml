<!--pages/set/index.wxml-->
<view class="container">
  <!-- 固定头部区域 -->
  <view class="fixed-header">
    <!-- 顶部状态栏 -->
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-back" bindtap="goBack">
        <van-icon name="arrow-left" size="20px" color="#333" />
      </view>
      <view class="nav-title">账号设置</view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="main-content" style="margin-top: {{statusBarHeight + 44}}px;">
    <!-- 设置内容区域 -->
    <view class="settings-content">
      <view class="settings-group">
        <!-- 个人信息修改 -->
        <view class="setting-item" bindtap="goToPersonalInfo">
          <view class="setting-icon">
            <image src="/icons/account_set.png" mode="aspectFit" class="icon-image"></image>
          </view>
          <view class="setting-text">个人信息修改</view>
          <view class="setting-arrow">
            <image src="/icons/arrow-right.png" mode="aspectFit" class="arrow-image"></image>
          </view>
        </view>

        <!-- 简易设置 -->
        <view class="setting-item" bindtap="goToSimpleSettings">
          <view class="setting-icon">
            <image src="/icons/settings_set.png" mode="aspectFit" class="icon-image"></image>
          </view>
          <view class="setting-text">隐私政策</view>
          <view class="setting-arrow">
            <image src="/icons/arrow-right.png" mode="aspectFit" class="arrow-image"></image>
          </view>
        </view>

        <!-- 注销账号 -->
        <view class="setting-item" bindtap="goToCancelAccount">
          <view class="setting-icon">
            <image src="/icons/cancel.png" mode="aspectFit" class="icon-image"></image>
          </view>
          <view class="setting-text">注销账号</view>
          <view class="setting-arrow">
            <image src="/icons/arrow-right.png" mode="aspectFit" class="arrow-image"></image>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>